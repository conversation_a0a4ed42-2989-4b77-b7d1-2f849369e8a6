# Audience Proximity Feature (trigger_type = 4)

## Overview
The Audience Proximity feature enables the Flutter digital signage player to detect audience presence using an LD2410B sensor via serial communication. When campaigns with `trigger_type = 4` are configured, the player establishes serial communication with the LD2410B sensor and triggers proximity-based campaigns when the detected distance is within the configured threshold.

## Technical Implementation

### 1. Serial Communication
- **Desktop (Linux/Windows)**: Uses `flutter_libserialport` package
- **Android**: Uses `usb_serial` package
- Cross-platform unified interface through `SerialCommunicationService`

### 2. Platform-Specific Configuration

#### Android
- **USB Permissions**: Added to `AndroidManifest.xml`
  ```xml
  <uses-permission android:name="android.permission.USB_PERMISSION" />
  <uses-feature android:name="android.hardware.usb.host" android:required="false" />
  ```
- **Device Detection**: Automatically detects LD2410B by vendor/product IDs:
  - FTDI: VID: 0x0403, PID: 0x6001
  - CH340: VID: 0x1a86, PID: 0x7523
- **Baud Rate**: 115200

#### Linux
- **Permissions**: User must be added to dialout group: `sudo usermod -aG dialout $USER`
- **Device Access**: Handles `/dev/ttyUSB*` or `/dev/ttyACM*` devices
- **Baud Rate**: 115200

#### Windows
- **COM Port Access**: Automatic COM port enumeration
- **Baud Rate**: 256000

### 3. LD2410B Data Parsing
The sensor data is parsed using the provided distance parsing function:
```dart
int? parseDistance(List<int> data) {
  for (int i = 0; i < data.length - 9; i++) {
    if (data[i] == 0xFD && data[i + 1] == 0xFC) {
      int distanceLow = data[i + 8];
      int distanceHigh = data[i + 9];
      return (distanceHigh << 8) + distanceLow;
    }
  }
  return null;
}
```

## Campaign Configuration

### Campaign JSON Structure
```json
{
  "id": "a1b2c3d4-e5f6-7890-abcd-ef1234567890",
  "name": "Audience Proximity Campaign",
  "start_date": "2025-01-01T00:00:00.000Z",
  "end_date": "2025-12-31T23:59:59.000Z",
  "status": "active",
  "trigger_type": 4,
  "distance": 100
}
```

### Required Fields for trigger_type = 4
- `trigger_type`: Must be set to `4`
- `distance`: Detection threshold in centimeters (e.g., 100 = 1 meter)

## Playback Logic

### State Machine
1. **Initialization**: 
   - Service checks for campaigns with `trigger_type = 4`
   - If found, initializes serial communication
   - Sets `playingAudienceProximity = false`

2. **Proximity Detection**:
   - **Distance ≤ threshold**: Start proximity campaign sequence
   - **Distance > threshold**: Continue current campaign until completion

3. **Campaign Sequencing**:
   - One proximity detection = one complete loop through ALL `trigger_type = 4` campaigns
   - Campaigns play in order defined in campaigns.json
   - After completion, returns to normal campaign flow

### Integration with Existing System
- Integrates seamlessly with `CampaignController` and `PlayerControllerService`
- Maintains compatibility with other trigger types (1, 2, 3)
- Uses existing media playback widgets and timing mechanisms
- Follows established campaign sequencing patterns

## Service Architecture

### SerialCommunicationService
- Singleton service for cross-platform serial communication
- Handles device detection, connection, and data reading
- Provides distance stream for real-time monitoring
- Implements graceful error handling and reconnection

### AudienceProximityService
- Manages proximity detection state machine
- Handles campaign sequencing for proximity campaigns
- Provides callbacks for campaign transitions
- Integrates with campaign controller for trigger checking

### Campaign Controller Integration
- Added `_checkAudienceProximityTrigger()` method
- Validates distance parameter and sensor connectivity
- Checks proximity state for campaign display decisions

## Error Handling
- Graceful fallback when sensor unavailable
- Comprehensive logging for debugging
- Device disconnection/reconnection handling
- Non-blocking initialization to prevent app startup delays

## Usage Instructions

### 1. Hardware Setup
1. Connect LD2410B sensor to device via USB
2. Ensure proper power supply to sensor
3. Verify device recognition in system

### 2. Campaign Configuration
1. Create campaigns with `trigger_type = 4` in campaigns.json
2. Set appropriate `distance` threshold in centimeters
3. Configure campaign content (schedule items, media)

### 3. Platform-Specific Setup

#### Linux
```bash
# Add user to dialout group
sudo usermod -aG dialout $USER
# Logout and login again for changes to take effect
```

#### Android
- Enable USB debugging
- Grant USB permissions when prompted
- Ensure USB OTG support on device

#### Windows
- Install appropriate USB drivers if needed
- Check Device Manager for COM port recognition

### 4. Testing
1. Start the player application
2. Check logs for serial communication initialization
3. Test proximity detection by moving within/outside threshold
4. Verify campaign transitions occur as expected

## Debugging

### Log Messages
- `SerialCommunicationService: Initializing for platform: [Platform]`
- `AudienceProximityService: Found X proximity campaigns`
- `AudienceProximityService: Current distance: Xcm, threshold: Ycm`
- `AudienceProximityService: Starting proximity campaign sequence`

### Common Issues
1. **Sensor not detected**: Check USB connection and permissions
2. **No distance readings**: Verify sensor power and data format
3. **Campaigns not triggering**: Check distance threshold and campaign configuration
4. **Permission errors**: Ensure proper platform-specific permissions

## Dependencies Added
```yaml
dependencies:
  flutter_libserialport: ^0.4.0  # Desktop serial communication
  usb_serial: ^0.5.0  # Android USB serial communication
```

## Files Modified/Created
- `lib/core/services/serial_communication_service.dart` (new)
- `lib/core/services/audience_proximity_service.dart` (new)
- `lib/core/services/campaign_controller.dart` (modified)
- `lib/ui/screens/player_screen.dart` (modified)
- `android/app/src/main/AndroidManifest.xml` (modified)
- `pubspec.yaml` (modified)
- `campaigns.json` (example campaign added)

This implementation provides a robust, cross-platform solution for audience proximity detection that integrates seamlessly with the existing digital signage player architecture.
