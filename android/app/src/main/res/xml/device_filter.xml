<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- LD2410B sensor typically uses FTDI or CH340 USB-to-serial chips -->
    
    <!-- 0x0403 / 0x6001: FTDI FT232R UART -->
    <usb-device vendor-id="1027" product-id="24577" />
    
    <!-- 0x0403 / 0x6015: FTDI FT231X -->
    <usb-device vendor-id="1027" product-id="24597" />
    
    <!-- 0x1a86 / 0x7523: CH340 USB-to-serial chip (common for LD2410B) -->
    <usb-device vendor-id="6790" product-id="29987" />
    
    <!-- 0x10C4 / 0xEA60: CP210x UART Bridge -->
    <usb-device vendor-id="4292" product-id="60000" />
    
    <!-- 0x067B / 0x2303: Prolific PL2303 -->
    <usb-device vendor-id="1659" product-id="8963" />
</resources>
