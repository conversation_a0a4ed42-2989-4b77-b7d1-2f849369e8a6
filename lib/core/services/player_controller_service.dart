import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:signage/core/controllers/system_menu_controller.dart';
import 'package:signage/core/models/settings.dart';
import 'package:signage/core/services/data_fetch_service.dart';
import 'package:signage/core/services/supabase_service.dart';
import 'package:signage/utils/network_utils.dart';
import 'package:signage/utils/platform_utils.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

/// Service for controlling the player in the background
class PlayerControllerService {
  // Singleton instance
  static final PlayerControllerService _instance = PlayerControllerService._internal();
  factory PlayerControllerService() => _instance;
  PlayerControllerService._internal();

  // Timer for periodic updates
  Timer? _updateTimer;

  // Supabase realtime subscription
  RealtimeChannel? _subscription;

  // Settings
  Settings? _settings;

  // Service state
  bool _isRunning = false;
  String? _screenId;
  String? _screenName;
  String? _location;
  String? _updateGuid;
  String? _updateFrequency;
  String? _startTime;
  String? _endTime;

  // Flag to prevent multiple simultaneous data refreshes
  bool _isRefreshingData = false;

  // Callback for when data is refreshed and player should restart
  Function? _onPlayerRestartNeeded;

  /// Start the background service
  Future<void> start({Function? onPlayerRestartNeeded}) async {
    if (_isRunning) {
      debugPrint('PlayerControllerService: Already running');
      return;
    }

    debugPrint('PlayerControllerService: Starting background service');
    _isRunning = true;
    _onPlayerRestartNeeded = onPlayerRestartNeeded;

    // Load settings
    await _loadSettings();

    // Check trial/subscription expiration
    await _checkTrialSubscriptionExpiration();

    // Start update timer
    _startUpdateTimer();

    // Subscribe to Supabase realtime updates
    _subscribeToRealtimeUpdates();

    // Test the realtime connection
    _testRealtimeConnection();
  }

  /// Test the realtime connection by logging the connection status
  void _testRealtimeConnection() {
    try {
      debugPrint('PlayerControllerService: Testing realtime connection');

      // Check if the subscription is active
      if (_subscription != null) {
        debugPrint('PlayerControllerService: Subscription is active');
      } else {
        debugPrint('PlayerControllerService: No active subscription');
      }

      // Log channel information
      final channelName = 'public:screens:$_screenId';
      debugPrint('PlayerControllerService: Channel name: $channelName');

      // Log that we're listening for updates
      debugPrint('PlayerControllerService: Listening for updates to screen $_screenId');
    } catch (e) {
      debugPrint('PlayerControllerService: Error testing realtime connection: $e');
    }
  }

  /// Stop the background service
  void stop() {
    debugPrint('PlayerControllerService: Stopping background service');
    _isRunning = false;

    // Reset the refresh flag
    _isRefreshingData = false;

    // Cancel timer
    _updateTimer?.cancel();
    _updateTimer = null;

    // Unsubscribe from Supabase realtime updates
    _subscription?.unsubscribe();
    _subscription = null;
  }

  /// Load settings from storage
  Future<void> _loadSettings() async {
    _settings = await Settings.load();
    if (_settings != null) {
      _screenId = _settings!.screenId;
      _screenName = _settings!.screenName;
      _location = _settings!.location;
      _updateFrequency = _settings!.updateFrequency;
      _startTime = _settings!.startTime;
      _endTime = _settings!.endTime;
      _updateGuid = _settings!.updateGuid;
      debugPrint('PlayerControllerService: Loaded settings for screen $_screenId');
      debugPrint('PlayerControllerService: Update frequency: ${_settings!.updateFrequency}');
    } else {
      debugPrint('PlayerControllerService: Failed to load settings');
    }
  }

  /// Check trial/subscription expiration and show dialog if expired
  Future<void> _checkTrialSubscriptionExpiration() async {
    if (_settings == null) {
      debugPrint('PlayerControllerService: Cannot check trial/subscription expiration, settings not loaded');
      return;
    }

    final trialEndsAt = _settings!.trialEndsAt;
    final subscriptionStatus = _settings!.subscriptionStatus;

    if (trialEndsAt == null) {
      debugPrint('PlayerControllerService: No trial end date found, skipping expiration check');
      return;
    }

    final now = DateTime.now().toUtc();
    debugPrint('PlayerControllerService: Checking trial/subscription expiration');
    debugPrint('PlayerControllerService: Current time: ${now.toIso8601String()}');
    debugPrint('PlayerControllerService: Trial ends at: ${trialEndsAt.toIso8601String()}');
    debugPrint('PlayerControllerService: Subscription status: $subscriptionStatus');

    // Check if current time has passed beyond trial_ends_at
    if (now.isAfter(trialEndsAt)) {
      debugPrint('PlayerControllerService: Trial/subscription has expired, showing dialog');
      _showTrialSubscriptionExpiredDialog(subscriptionStatus);
    } else {
      debugPrint('PlayerControllerService: Trial/subscription is still active');
    }
  }

  /// Start the update timer based on settings.updateFrequency
  void _startUpdateTimer() {
    // Cancel any existing timer
    _updateTimer?.cancel();

    if (_settings == null) {
      debugPrint('PlayerControllerService: Cannot start timer, settings not loaded');
      return;
    }

    // Parse the update frequency (format: HH:mm:ss)
    final updateFrequency = _settings!.updateFrequency ?? '00:00:30'; // Default to 30 seconds
    final parts = updateFrequency.split(':');

    if (parts.length != 3) {
      debugPrint('PlayerControllerService: Invalid update frequency format: $updateFrequency, using default (30 seconds)');
      _updateTimer = Timer.periodic(const Duration(seconds: 30), _onTimerTick);
      return;
    }

    try {
      final hours = int.parse(parts[0]);
      final minutes = int.parse(parts[1]);
      final seconds = int.parse(parts[2]);

      final totalSeconds = (hours * 3600) + (minutes * 60) + seconds;

      if (totalSeconds <= 0) {
        debugPrint('PlayerControllerService: Invalid update frequency (zero or negative), using default (30 seconds)');
        _updateTimer = Timer.periodic(const Duration(seconds: 30), _onTimerTick);
      } else {
        debugPrint('PlayerControllerService: Setting update timer to $totalSeconds seconds');
        _updateTimer = Timer.periodic(Duration(seconds: totalSeconds), _onTimerTick);
      }
    } catch (e) {
      debugPrint('PlayerControllerService: Error parsing update frequency: $e, using default (30 seconds)');
      _updateTimer = Timer.periodic(const Duration(seconds: 30), _onTimerTick);
    }
  }

  /// Timer tick handler - runs asynchronously to avoid blocking UI
  void _onTimerTick(Timer timer) {
    if (!_isRunning || _screenId == null) {
      return;
    }

    // Run the update operations asynchronously without blocking
    _performBackgroundUpdate();
  }

  /// Perform background update operations asynchronously
  Future<void> _performBackgroundUpdate() async {
    try {
      debugPrint('PlayerControllerService: Timer tick, checking internet connection');

      // Check internet connectivity
      final hasInternet = await NetworkUtils.hasInternetConnection();
      if (!hasInternet) {
        debugPrint('PlayerControllerService: No internet connection, skipping update');
        return;
      }

      // Update screen details in screens table
      await _updateScreenDetails();
    } catch (e) {
      debugPrint('PlayerControllerService: Error during background update: $e');
    }
  }

  /// Update screen details including last_ping_at and health information in screens table
  Future<void> _updateScreenDetails() async {
    if (_screenId == null) {
      debugPrint('PlayerControllerService: Cannot update screen details, screen ID not available');
      return;
    }

    try {
      debugPrint('PlayerControllerService: Updating screen details for screen $_screenId');

      // Get system health information
      final jsonHealth = await _getSystemHealthInfo();

      // Update screen details in Supabase (run in background without blocking)
      SupabaseService.updateScreenDetails(_screenId!, jsonHealth).then((result) {
        if (result) {
          debugPrint('PlayerControllerService: Successfully updated screen details');
        } else {
          debugPrint('PlayerControllerService: Failed to update screen details');
        }
      }).catchError((e) {
        debugPrint('PlayerControllerService: Error updating screen details: $e');
      });
    } catch (e) {
      debugPrint('PlayerControllerService: Error preparing screen details update: $e');
    }
  }

  /// Get system health information based on the platform
  Future<Map<String, dynamic>> _getSystemHealthInfo() async {
    final Map<String, dynamic> healthInfo = {};

    try {
      // Get app version
      try {
        final packageInfo = await PackageInfo.fromPlatform();
        healthInfo['app_version'] = packageInfo.version;
        healthInfo['build_number'] = packageInfo.buildNumber;
        healthInfo['package_name'] = packageInfo.packageName;
      } catch (e) {
        debugPrint('PlayerControllerService: Error getting app version: $e');
        healthInfo['app_version'] = 'unknown';
      }

      // Use a more reliable approach for getting system health information
      // For Android, we can use the ProcessInfo class
      if (PlatformUtils.isAndroid) {
        try {
          // Get memory info
          final memoryInfo = await _getAndroidMemoryInfo();
          healthInfo.addAll(memoryInfo);

          // Get disk/storage info
          final diskInfo = await _getAndroidDiskInfo();
          healthInfo.addAll(diskInfo);
        } catch (e) {
          debugPrint('PlayerControllerService: Error getting Android system info: $e');
          healthInfo['android_info_error'] = e.toString();
        }
      } else if (PlatformUtils.isLinux) {
        try {
          // Get Linux system info using command line tools
          final linuxInfo = await _getLinuxSystemInfo();
          healthInfo.addAll(linuxInfo);
        } catch (e) {
          debugPrint('PlayerControllerService: Error getting Linux system info: $e');
          healthInfo['linux_info_error'] = e.toString();
        }
      } else if (PlatformUtils.isWindows) {
        try {
          // Get Windows system info
          final windowsInfo = await _getWindowsSystemInfo();
          healthInfo.addAll(windowsInfo);
        } catch (e) {
          debugPrint('PlayerControllerService: Error getting Windows system info: $e');
          healthInfo['windows_info_error'] = e.toString();
        }
      }

      // Add platform information
      healthInfo['platform'] = PlatformUtils.platformName;

      // Add timestamp
      healthInfo['timestamp'] = DateTime.now().toUtc().toIso8601String();

      debugPrint('PlayerControllerService: System health info: $healthInfo');
      return healthInfo;
    } catch (e) {
      debugPrint('PlayerControllerService: Error getting system health info: $e');
      return {
        'error': e.toString(),
        'timestamp': DateTime.now().toUtc().toIso8601String()
      };
    }
  }

  /// Get Android memory information
  Future<Map<String, dynamic>> _getAndroidMemoryInfo() async {
    try {
      final Map<String, dynamic> memoryInfo = {};

      // Use ActivityManager to get memory info on Android
      // This is a simplified approach - in a real app, you would use platform channels
      // to access the Android ActivityManager API

      // For now, we'll use a simpler approach by reading /proc/meminfo
      if (await File('/proc/meminfo').exists()) {
        final memInfoContent = await File('/proc/meminfo').readAsString();
        final lines = memInfoContent.split('\n');

        for (final line in lines) {
          if (line.contains('MemTotal')) {
            final parts = line.split(':');
            if (parts.length > 1) {
              final valueStr = parts[1].trim().split(' ')[0];
              memoryInfo['memory_total'] = int.tryParse(valueStr) ?? 0;
            }
          } else if (line.contains('MemFree')) {
            final parts = line.split(':');
            if (parts.length > 1) {
              final valueStr = parts[1].trim().split(' ')[0];
              memoryInfo['memory_free'] = int.tryParse(valueStr) ?? 0;
            }
          } else if (line.contains('MemAvailable')) {
            final parts = line.split(':');
            if (parts.length > 1) {
              final valueStr = parts[1].trim().split(' ')[0];
              memoryInfo['memory_available'] = int.tryParse(valueStr) ?? 0;
            }
          }
        }

        // Calculate used memory and percentage
        if (memoryInfo.containsKey('memory_total') && memoryInfo.containsKey('memory_free')) {
          memoryInfo['memory_used'] = memoryInfo['memory_total'] - memoryInfo['memory_free'];
          memoryInfo['memory_usage_percentage'] =
              (memoryInfo['memory_used'] / memoryInfo['memory_total'] * 100).toStringAsFixed(2);
        }
      }

      return memoryInfo;
    } catch (e) {
      debugPrint('PlayerControllerService: Error getting Android memory info: $e');
      return {'memory_error': e.toString()};
    }
  }

  /// Get Android disk/storage information
  Future<Map<String, dynamic>> _getAndroidDiskInfo() async {
    try {
      final Map<String, dynamic> diskInfo = {};

      // Get the application's directory to check storage
      final appDir = await Directory('/data/data').exists()
          ? Directory('/data/data')
          : Directory('/');

      // Get external storage directory if available
      final externalDir = await Directory('/storage/emulated/0').exists()
          ? Directory('/storage/emulated/0')
          : null;

      // Use df command to get disk usage for internal storage
      try {
        final result = await Process.run('df', ['-h', appDir.path]);
        if (result.exitCode == 0) {
          final lines = (result.stdout as String).split('\n');
          if (lines.length >= 2) {
            final dfParts = lines[1].split(RegExp(r'\s+'));
            if (dfParts.length >= 5) {
              diskInfo['internal_storage_total'] = dfParts[1];
              diskInfo['internal_storage_used'] = dfParts[2];
              diskInfo['internal_storage_available'] = dfParts[3];
              diskInfo['internal_storage_usage_percentage'] = dfParts[4].replaceAll('%', '');
            }
          }
        }
      } catch (e) {
        debugPrint('PlayerControllerService: Error getting internal storage info: $e');
        diskInfo['internal_storage_error'] = e.toString();
      }

      // Use df command to get disk usage for external storage if available
      if (externalDir != null) {
        try {
          final result = await Process.run('df', ['-h', externalDir.path]);
          if (result.exitCode == 0) {
            final lines = (result.stdout as String).split('\n');
            if (lines.length >= 2) {
              final dfParts = lines[1].split(RegExp(r'\s+'));
              if (dfParts.length >= 5) {
                diskInfo['external_storage_total'] = dfParts[1];
                diskInfo['external_storage_used'] = dfParts[2];
                diskInfo['external_storage_available'] = dfParts[3];
                diskInfo['external_storage_usage_percentage'] = dfParts[4].replaceAll('%', '');
              }
            }
          }
        } catch (e) {
          debugPrint('PlayerControllerService: Error getting external storage info: $e');
          diskInfo['external_storage_error'] = e.toString();
        }
      }

      // If we couldn't get detailed info, try a simpler approach
      if (diskInfo.isEmpty) {
        try {
          // Get the application's directory to check storage
          final appDir = Directory('/data/data');

          // Get the total and free space
          final statFs = await Process.run('df', ['-k', appDir.path]);
          if (statFs.exitCode == 0) {
            final lines = (statFs.stdout as String).split('\n');
            if (lines.length >= 2) {
              final parts = lines[1].split(RegExp(r'\s+'));
              if (parts.length >= 4) {
                final total = int.tryParse(parts[1]) ?? 0;
                final used = int.tryParse(parts[2]) ?? 0;
                final available = int.tryParse(parts[3]) ?? 0;

                diskInfo['disk_total'] = total;
                diskInfo['disk_used'] = used;
                diskInfo['disk_available'] = available;
                diskInfo['disk_usage_percentage'] =
                    total > 0 ? ((used / total) * 100).toStringAsFixed(2) : '0';
              }
            }
          }
        } catch (e) {
          debugPrint('PlayerControllerService: Error getting simple disk info: $e');
          diskInfo['disk_error'] = e.toString();
        }
      }

      return diskInfo;
    } catch (e) {
      debugPrint('PlayerControllerService: Error getting Android disk info: $e');
      return {'disk_error': e.toString()};
    }
  }

  /// Get Linux system information
  Future<Map<String, dynamic>> _getLinuxSystemInfo() async {
    final Map<String, dynamic> systemInfo = {};

    try {
      // CPU info
      if (await File('/proc/stat').exists()) {
        final statContent = await File('/proc/stat').readAsString();
        final lines = statContent.split('\n');

        if (lines.isNotEmpty && lines[0].startsWith('cpu ')) {
          final cpuLine = lines[0];
          final parts = cpuLine.split(' ').where((part) => part.isNotEmpty).toList();

          if (parts.length >= 5) {
            final user = int.tryParse(parts[1]) ?? 0;
            final nice = int.tryParse(parts[2]) ?? 0;
            final system = int.tryParse(parts[3]) ?? 0;
            final idle = int.tryParse(parts[4]) ?? 0;

            final total = user + nice + system + idle;
            final usage = total - idle;

            systemInfo['cpu_usage_percentage'] =
                (usage / total * 100).toStringAsFixed(2);
          }
        }
      }

      // Memory info
      if (await File('/proc/meminfo').exists()) {
        final memInfoContent = await File('/proc/meminfo').readAsString();
        final memInfo = <String, int>{};

        for (final line in memInfoContent.split('\n')) {
          final parts = line.split(':');
          if (parts.length == 2) {
            final key = parts[0].trim();
            final valueStr = parts[1].trim().split(' ')[0];
            final value = int.tryParse(valueStr);

            if (value != null) {
              memInfo[key] = value;
            }
          }
        }

        if (memInfo.containsKey('MemTotal') && memInfo.containsKey('MemFree')) {
          final total = memInfo['MemTotal']!;
          final free = memInfo['MemFree']!;
          final used = total - free;

          systemInfo['memory_total'] = total;
          systemInfo['memory_free'] = free;
          systemInfo['memory_used'] = used;
          systemInfo['memory_usage_percentage'] =
              (used / total * 100).toStringAsFixed(2);
        }
      }

      // Disk info - get all mounted filesystems
      try {
        final result = await Process.run('df', ['-k']);
        if (result.exitCode == 0) {
          final lines = (result.stdout as String).split('\n');

          // Skip the header line
          for (int i = 1; i < lines.length; i++) {
            final line = lines[i].trim();
            if (line.isEmpty) continue;

            final dfParts = line.split(RegExp(r'\s+'));
            if (dfParts.length >= 6) {
              final filesystem = dfParts[0];
              final mountpoint = dfParts[5];
              final total = int.tryParse(dfParts[1]) ?? 0;
              final used = int.tryParse(dfParts[2]) ?? 0;
              final available = int.tryParse(dfParts[3]) ?? 0;
              final usagePercentage = dfParts[4].replaceAll('%', '');

              // Only include important filesystems
              if (mountpoint == '/' ||
                  mountpoint == '/home' ||
                  mountpoint.startsWith('/media/') ||
                  mountpoint.startsWith('/mnt/')) {

                final diskKey = 'disk_${mountpoint.replaceAll('/', '_')}';
                final cleanKey = diskKey.endsWith('_') ? diskKey.substring(0, diskKey.length - 1) : diskKey;

                systemInfo['${cleanKey}_filesystem'] = filesystem;
                systemInfo['${cleanKey}_total'] = total;
                systemInfo['${cleanKey}_used'] = used;
                systemInfo['${cleanKey}_available'] = available;
                systemInfo['${cleanKey}_usage_percentage'] = usagePercentage;
              }
            }
          }
        }
      } catch (e) {
        debugPrint('PlayerControllerService: Error getting Linux disk info: $e');
        systemInfo['disk_error'] = e.toString();

        // Fallback to a simpler approach for root filesystem only
        try {
          final result = await Process.run('df', ['-k', '/']);
          if (result.exitCode == 0) {
            final lines = (result.stdout as String).split('\n');
            if (lines.length >= 2) {
              final dfParts = lines[1].split(RegExp(r'\s+'));
              if (dfParts.length >= 5) {
                final total = int.tryParse(dfParts[1]) ?? 0;
                final used = int.tryParse(dfParts[2]) ?? 0;
                final available = int.tryParse(dfParts[3]) ?? 0;
                final usagePercentage = dfParts[4].replaceAll('%', '');

                systemInfo['disk_root_total'] = total;
                systemInfo['disk_root_used'] = used;
                systemInfo['disk_root_available'] = available;
                systemInfo['disk_root_usage_percentage'] = usagePercentage;
              }
            }
          }
        } catch (e) {
          debugPrint('PlayerControllerService: Error getting Linux root disk info: $e');
        }
      }

      return systemInfo;
    } catch (e) {
      debugPrint('PlayerControllerService: Error getting Linux system info: $e');
      return {'error': e.toString()};
    }
  }

  /// Get Windows system information
  Future<Map<String, dynamic>> _getWindowsSystemInfo() async {
    final Map<String, dynamic> systemInfo = {};

    try {
      // Memory info - use wmic command
      try {
        final result = await Process.run('wmic', ['OS', 'get', 'FreePhysicalMemory,TotalVisibleMemorySize']);
        if (result.exitCode == 0) {
          final lines = (result.stdout as String).split('\n');
          if (lines.length >= 2) {
            final parts = lines[1].trim().split(RegExp(r'\s+'));
            if (parts.length >= 2) {
              final freeMemory = int.tryParse(parts[0]) ?? 0;
              final totalMemory = int.tryParse(parts[1]) ?? 0;
              final usedMemory = totalMemory - freeMemory;

              systemInfo['memory_total'] = totalMemory;
              systemInfo['memory_free'] = freeMemory;
              systemInfo['memory_used'] = usedMemory;
              systemInfo['memory_usage_percentage'] =
                  totalMemory > 0 ? ((usedMemory / totalMemory) * 100).toStringAsFixed(2) : '0';
            }
          }
        }
      } catch (e) {
        debugPrint('PlayerControllerService: Error getting Windows memory info: $e');
        systemInfo['memory_error'] = e.toString();
      }

      // CPU info - use wmic command
      try {
        final result = await Process.run('wmic', ['cpu', 'get', 'loadpercentage']);
        if (result.exitCode == 0) {
          final lines = (result.stdout as String).split('\n');
          if (lines.length >= 2) {
            final cpuUsage = int.tryParse(lines[1].trim()) ?? 0;
            systemInfo['cpu_usage_percentage'] = cpuUsage.toString();
          }
        }
      } catch (e) {
        debugPrint('PlayerControllerService: Error getting Windows CPU info: $e');
        systemInfo['cpu_error'] = e.toString();
      }

      // Disk info - use wmic command
      try {
        final result = await Process.run('wmic', ['logicaldisk', 'get', 'DeviceID,FreeSpace,Size']);
        if (result.exitCode == 0) {
          final lines = (result.stdout as String).split('\n')
              .where((line) => line.trim().isNotEmpty)
              .toList();

          if (lines.length >= 2) {
            // Skip the header line
            for (int i = 1; i < lines.length; i++) {
              final parts = lines[i].trim().split(RegExp(r'\s+'));
              if (parts.length >= 3) {
                final driveId = parts[0];
                final freeSpace = int.tryParse(parts[1]) ?? 0;
                final totalSize = int.tryParse(parts[2]) ?? 0;
                final usedSpace = totalSize - freeSpace;

                // Only include drives with valid data
                if (totalSize > 0) {
                  final diskKey = 'disk_${driveId.replaceAll(':', '')}';
                  systemInfo['${diskKey}_total'] = totalSize;
                  systemInfo['${diskKey}_free'] = freeSpace;
                  systemInfo['${diskKey}_used'] = usedSpace;
                  systemInfo['${diskKey}_usage_percentage'] =
                      ((usedSpace / totalSize) * 100).toStringAsFixed(2);
                }
              }
            }
          }
        }
      } catch (e) {
        debugPrint('PlayerControllerService: Error getting Windows disk info: $e');
        systemInfo['disk_error'] = e.toString();

        // Fallback to a simpler approach using dir command
        try {
          final result = await Process.run('cmd', ['/c', 'dir', 'C:\\']);
          if (result.exitCode == 0) {
            final output = result.stdout as String;
            final lines = output.split('\n');

            // Look for the line with disk information
            for (final line in lines) {
              if (line.contains('bytes free')) {
                final parts = line.trim().split(RegExp(r'\s+'));
                if (parts.length >= 3) {
                  // Extract the free space
                  final freeSpaceStr = parts.firstWhere(
                    (part) => part.contains(',') && int.tryParse(part.replaceAll(',', '')) != null,
                    orElse: () => '0'
                  );
                  final freeSpace = int.tryParse(freeSpaceStr.replaceAll(',', '')) ?? 0;

                  systemInfo['disk_C_free'] = freeSpace;
                }
              }
            }
          }
        } catch (e) {
          debugPrint('PlayerControllerService: Error getting Windows disk info using fallback: $e');
        }
      }

      return systemInfo;
    } catch (e) {
      debugPrint('PlayerControllerService: Error getting Windows system info: $e');
      return {'error': e.toString()};
    }
  }

  /// Subscribe to Supabase realtime updates for the screens table
  void _subscribeToRealtimeUpdates() {
    if (_screenId == null) {
      debugPrint('PlayerControllerService: Cannot subscribe to realtime updates, screen ID not available');
      return;
    }

    // Unsubscribe from any existing subscription
    _subscription?.unsubscribe();

    debugPrint('PlayerControllerService: Subscribing to realtime updates for screen $_screenId');

    // Create a more specific channel name with the screen ID
    final channelName = 'public:screens:$_screenId';
    debugPrint('PlayerControllerService: Using channel name: $channelName');

    _subscription = SupabaseService.client
        .channel(channelName)
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'screens',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'id',
            value: _screenId,
          ),
          callback: _handleScreenUpdate,
        )
        .subscribe();
  }

  /// Handle screen update from Supabase realtime
  void _handleScreenUpdate(PostgresChangePayload payload) async {
    if (!_isRunning || _screenId == null) {
      debugPrint('PlayerControllerService: Service not running or screen ID not available, ignoring update');
      return;
    }

    debugPrint('PlayerControllerService: Received realtime update payload: ${payload.toString()}');

    try {
      final Map<String, dynamic> newRow = payload.newRecord;
      final Map<String, dynamic> oldRow = payload.oldRecord;

      // Log the id and updateguid for debugging
      debugPrint('***************************************************************');
      debugPrint('PlayerControllerService: New row ID: ${newRow['id']}');
      debugPrint('PlayerControllerService: Old row ID: ${oldRow['id']}');
      debugPrint('PlayerControllerService: New update GUID: ${newRow['updateguid']}');
      debugPrint('PlayerControllerService: Old update GUID: ${_settings!.updateGuid}');
      debugPrint('PlayerControllerService: New is_deleted: ${newRow['is_deleted']}');
      debugPrint('***************************************************************');

      // Check if screen is deleted first, before checking updateGuid
      final String updatedScreenId = newRow['id'].toString();
      if (updatedScreenId == _screenId) {
        final bool isDeleted = newRow['is_deleted'] == true;

        if (isDeleted) {
          // Screen is deleted - show permanent popup
          final String screenName = newRow['name'] ?? _screenName ?? 'Unknown Screen';
          final String screenCode = newRow['code'] ?? _settings?.code ?? 'Unknown Code';

          debugPrint('PlayerControllerService: Screen is deleted, showing permanent popup');
          _showScreenDeletedDialog(screenName, screenCode);
          return;
        }
      }

      if (newRow['id'] == _screenId && newRow['updateguid'] == _settings!.updateGuid) {
       debugPrint('PlayerControllerService: No Updates');
       return;
      }

      final String newUpdateGuid = newRow['updateguid'] ?? '';
      final String newScreenName = newRow['name'] ?? '';
      final String newLocation = newRow['location'] ?? '';
      final String newStartTime = newRow['start_time'] ?? '';
      final String newEndTime = newRow['end_time'] ?? '';
      final String newUpdateFrequency = newRow['update_frequency'] ?? '';
      final String oldUpdateGuid = _settings!.updateGuid ?? '';

      debugPrint('PlayerControllerService: Received screen update for screen $updatedScreenId');
      debugPrint('PlayerControllerService: Current screen ID: $_screenId');
      debugPrint('PlayerControllerService: New update GUID: $newUpdateGuid');
      debugPrint('PlayerControllerService: Old update GUID: $oldUpdateGuid');

      // Only respond if this is the device's own screen_id and the updateguid has changed
      if (updatedScreenId == _screenId && newUpdateGuid != oldUpdateGuid) {
        debugPrint('PlayerControllerService: This device\'s screen info was updated!');
        debugPrint('PlayerControllerService: Current stored update GUID: $_updateGuid');
        debugPrint('PlayerControllerService: New update GUID from server: $newUpdateGuid');

        // Check if we're already refreshing data
        if (_isRefreshingData) {
          debugPrint('PlayerControllerService: Data refresh already in progress, queueing this update');
          // Store the new GUID but don't trigger a refresh yet
          _updateGuid = newUpdateGuid;
          _screenName = newScreenName;
          _location = newLocation;
          _startTime = newStartTime;
          _endTime = newEndTime;
          _updateFrequency = newUpdateFrequency;

          return;
        }

        // Check internet connectivity
        final hasInternet = await NetworkUtils.hasInternetConnection();
        if (!hasInternet) {
          debugPrint('PlayerControllerService: No internet connection, skipping data refresh');
          return;
        }

        // Update the stored update GUID
        _updateGuid = newUpdateGuid;
        _screenName = newScreenName;
        _location = newLocation;
        _startTime = newStartTime;
        _endTime = newEndTime;
        _updateFrequency = newUpdateFrequency;

        // Trigger data refresh
        debugPrint('PlayerControllerService: Starting data refresh due to updateguid change');
        await _refreshData();
      } else {
        if (updatedScreenId != _screenId) {
          debugPrint('PlayerControllerService: Update is for a different screen, ignoring');
        } else if (newUpdateGuid == oldUpdateGuid) {
          debugPrint('PlayerControllerService: Update GUID has not changed, ignoring update');
        } else if (newUpdateGuid.isEmpty) {
          debugPrint('PlayerControllerService: New update GUID is empty, ignoring update');
        }
      }
    } catch (e) {
      debugPrint('PlayerControllerService: Error handling screen update: $e');
    }
  }

  /// Refresh data from Supabase
  Future<void> _refreshData() async {
    // Prevent multiple simultaneous data refreshes
    if (_isRefreshingData) {
      debugPrint('PlayerControllerService: Data refresh already in progress, skipping');
      return;
    }

    try {
      _isRefreshingData = true;
      debugPrint('PlayerControllerService: Refreshing data');

      // Create a DataFetchService instance for background refresh
      final dataFetchService = DataFetchService(
        onProgress: (progress, message) {
          debugPrint('PlayerControllerService: Data refresh progress: $progress - $message');
        },
        onComplete: () async {
          debugPrint('PlayerControllerService: Data refresh complete, restarting player');

          // Update settings with new update GUID
          try {
            await _updateSettingsWithNewGuid();

            // Add a small delay to ensure settings file is fully written and flushed to disk
            await Future.delayed(const Duration(milliseconds: 100));

            // Verify settings can be loaded successfully before proceeding
            final verifySettings = await Settings.load();
            if (verifySettings == null) {
              debugPrint('PlayerControllerService: Warning - Settings verification failed after update');
            }
          } catch (e) {
            debugPrint('PlayerControllerService: Error updating settings after data refresh: $e');
            // Continue with restart even if settings update fails
          }

          // Call the restart callback if provided
          if (_onPlayerRestartNeeded != null) {
            _onPlayerRestartNeeded!();
          }

          // Reset the refresh flag
          _isRefreshingData = false;
        },
        onError: (error) {
          debugPrint('PlayerControllerService: Data refresh error: $error');
          // Reset the refresh flag even on error
          _isRefreshingData = false;
        },
      );

      // Fetch all data
      await dataFetchService.fetchAllData();
    } catch (e) {
      debugPrint('PlayerControllerService: Unexpected error during data refresh: $e');
      // Always reset the refresh flag
      _isRefreshingData = false;
    }
  }

  /// Update settings.json with the new update GUID
  Future<void> _updateSettingsWithNewGuid() async {
    if (_settings == null || _updateGuid == null) {
      return;
    }

    try {
      // Fetch latest trial and subscription data from screen_registrations table
      final trialSubscriptionData = await SupabaseService.fetchTrialSubscriptionData(_settings!.screenId);

      DateTime? trialEndsAt = _settings!.trialEndsAt;
      String? subscriptionStatus = _settings!.subscriptionStatus;

      if (trialSubscriptionData != null) {
        trialEndsAt = trialSubscriptionData['trial_ends_at'] != null
            ? DateTime.parse(trialSubscriptionData['trial_ends_at'])
            : null;
        subscriptionStatus = trialSubscriptionData['subscription_status']?.toString();
        debugPrint('PlayerControllerService: Updated trial/subscription data from server');
      } else {
        debugPrint('PlayerControllerService: Failed to fetch trial/subscription data, using existing values');
      }

      // Create a new settings object with the updated GUID and trial/subscription data
      final updatedSettings = Settings(
        screenId: _settings!.screenId,
        screenName: _screenName ?? _settings!.screenName,
        code: _settings!.code,
        location: _location ?? _settings!.location,
        description: _settings!.description,
        metadata: _settings!.metadata,
        registeredAt: _settings!.registeredAt,
        updateFrequency: _updateFrequency ?? _settings!.updateFrequency,
        updateGuid: _updateGuid,
        startTime: _startTime ?? _settings!.startTime,
        endTime: _endTime ?? _settings!.endTime,
        trialEndsAt: trialEndsAt,
        subscriptionStatus: subscriptionStatus,
      );

      // Save the updated settings
      await updatedSettings.save();

      // Update the current settings
      _settings = updatedSettings;

      debugPrint('PlayerControllerService: Updated settings with new update GUID: $_updateGuid');
    } catch (e) {
      debugPrint('PlayerControllerService: Error updating settings with new update GUID: $e');
    }
  }

  /// Show a permanent dialog when the screen is deleted
  void _showScreenDeletedDialog(String screenName, String screenCode) {
    // Use the global navigator key to show the dialog
    final context = SystemMenuController.navigatorKey.currentContext;

    if (context == null) {
      debugPrint('PlayerControllerService: Cannot show screen deleted dialog - no context available');
      return;
    }

    // Show a non-dismissible dialog
    showDialog(
      context: context,
      barrierDismissible: false, // User cannot dismiss by tapping outside
      builder: (BuildContext context) {
        return PopScope(
          canPop: false, // Prevent back button from closing dialog
          child: AlertDialog(
            title: const Text(
              'Screen Retired',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                Text(
                  'Name of the Screen:',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  screenName,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                Text(
                  'Code of the Screen:',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  screenCode,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    border: Border.all(color: Colors.red.shade200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'Screen is retired. Please contact your administrator!',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            // No actions - dialog cannot be closed
          ),
        );
      },
    );
  }

  /// Show a permanent dialog when trial/subscription has expired
  void _showTrialSubscriptionExpiredDialog(String? subscriptionStatus) {
    // Use the global navigator key to show the dialog
    final context = SystemMenuController.navigatorKey.currentContext;

    if (context == null) {
      debugPrint('PlayerControllerService: Cannot show trial/subscription expired dialog - no context available');
      return;
    }

    // Determine dialog title and message based on subscription status
    final bool isTrial = subscriptionStatus == 'trial';
    final String dialogTitle = isTrial ? 'Trial Ended' : 'Subscription Ended';
    final String dialogMessage = isTrial
        ? 'Trial period has ended. Please renew the screen subscription!'
        : 'Subscription period has ended. Please renew the screen subscription!';

    final String screenName = _settings?.screenName ?? 'Unknown Screen';
    final String screenCode = _settings?.code ?? 'Unknown Code';

    // Show a non-dismissible dialog
    showDialog(
      context: context,
      barrierDismissible: false, // User cannot dismiss by tapping outside
      builder: (BuildContext context) {
        return PopScope(
          canPop: false, // Prevent back button from closing dialog
          child: AlertDialog(
            title: Text(
              dialogTitle,
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 16),
                const Text(
                  'Name of the Screen:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  screenName,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 16),
                const Text(
                  'Code of the Screen:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  screenCode,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.red.shade50,
                    border: Border.all(color: Colors.red.shade200),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    dialogMessage,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
            // No actions - dialog cannot be closed
          ),
        );
      },
    );
  }
}
