import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:signage/utils/platform_utils.dart';

// Platform-specific imports
import 'package:flutter_libserialport/flutter_libserialport.dart' if (dart.library.html) 'dart:html';
import 'package:usb_serial/usb_serial.dart' if (dart.library.html) 'dart:html';

/// Cross-platform serial communication service for LD2410B sensor
class SerialCommunicationService {
  static final SerialCommunicationService _instance = SerialCommunicationService._internal();
  factory SerialCommunicationService() => _instance;
  SerialCommunicationService._internal();

  // Platform-specific controllers
  SerialPort? _desktopPort;
  UsbPort? _androidPort;
  
  // Connection state
  bool _isConnected = false;
  bool _isInitialized = false;
  
  // Data stream
  StreamController<int?>? _distanceController;
  Stream<int?>? _distanceStream;
  
  // Buffer for incoming data
  List<int> _dataBuffer = [];
  
  // Timer for periodic data reading
  Timer? _readTimer;

  /// Get the distance stream
  Stream<int?>? get distanceStream => _distanceStream;

  /// Check if the service is connected
  bool get isConnected => _isConnected;

  /// Initialize the serial communication service
  Future<bool> initialize() async {
    if (_isInitialized) {
      debugPrint('SerialCommunicationService: Already initialized');
      return _isConnected;
    }

    debugPrint('SerialCommunicationService: Initializing for platform: ${PlatformUtils.platformName}');

    try {
      _distanceController = StreamController<int?>.broadcast();
      _distanceStream = _distanceController!.stream;

      if (PlatformUtils.isAndroid) {
        await _initializeAndroid();
      } else if (PlatformUtils.isDesktop) {
        await _initializeDesktop();
      } else {
        debugPrint('SerialCommunicationService: Unsupported platform');
        _isInitialized = true; // Mark as initialized to prevent retries
        return false;
      }

      _isInitialized = true;
      debugPrint('SerialCommunicationService: Initialization completed. Connected: $_isConnected');
      return _isConnected;
    } catch (e) {
      debugPrint('SerialCommunicationService: Initialization failed: $e');
      _isInitialized = true; // Mark as initialized to prevent retries
      return false;
    }
  }

  /// Initialize Android USB serial communication
  Future<void> _initializeAndroid() async {
    try {
      debugPrint('SerialCommunicationService: Initializing Android USB serial');

      // Get list of available USB devices
      List<UsbDevice> devices = await UsbSerial.listDevices();
      debugPrint('SerialCommunicationService: Found ${devices.length} USB devices');

      // Look for LD2410B device (you may need to adjust vendor/product IDs)
      UsbDevice? targetDevice;
      for (UsbDevice device in devices) {
        debugPrint('SerialCommunicationService: Device - VID: ${device.vid}, PID: ${device.pid}');
        // LD2410B typically uses FTDI or CH340 chip
        // Common VID/PID combinations: 0x0403/0x6001 (FTDI), 0x1a86/0x7523 (CH340)
        if ((device.vid == 0x0403 && device.pid == 0x6001) ||
            (device.vid == 0x1a86 && device.pid == 0x7523)) {
          targetDevice = device;
          break;
        }
      }

      if (targetDevice == null) {
        debugPrint('SerialCommunicationService: LD2410B device not found');
        return;
      }

      debugPrint('SerialCommunicationService: Found LD2410B device');

      // Create USB port using the correct API
      _androidPort = await targetDevice.create();

      if (_androidPort == null) {
        debugPrint('SerialCommunicationService: Failed to create USB port');
        return;
      }

      // Open the port
      bool openResult = await _androidPort!.open();
      if (!openResult) {
        debugPrint('SerialCommunicationService: Failed to open USB port');
        return;
      }

      // Configure port settings
      await _androidPort!.setDTR(true);
      await _androidPort!.setRTS(true);
      await _androidPort!.setPortParameters(115200, UsbPort.DATABITS_8, UsbPort.STOPBITS_1, UsbPort.PARITY_NONE);

      _isConnected = true;
      debugPrint('SerialCommunicationService: Android USB port opened successfully');

      // Start reading data
      _startAndroidDataReading();

    } catch (e) {
      debugPrint('SerialCommunicationService: Android initialization error: $e');
    }
  }

  /// Initialize desktop serial communication
  Future<void> _initializeDesktop() async {
    try {
      debugPrint('SerialCommunicationService: Initializing desktop serial');

      // Add timeout to prevent hanging
      await Future.any([
        _initializeDesktopWithTimeout(),
        Future.delayed(const Duration(seconds: 10), () {
          debugPrint('SerialCommunicationService: Desktop initialization timeout after 10 seconds');
        }),
      ]);

    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop initialization error: $e');
    }
  }

  /// Initialize desktop serial communication with timeout protection
  Future<void> _initializeDesktopWithTimeout() async {
    try {
      debugPrint('SerialCommunicationService: Starting desktop serial initialization');

      // Get list of available serial ports
      final availablePorts = SerialPort.availablePorts;
      debugPrint('SerialCommunicationService: Available ports: $availablePorts');

      if (availablePorts.isEmpty) {
        debugPrint('SerialCommunicationService: No serial ports available');
        return;
      }
      
      // Try to find LD2410B device by testing each port
      for (String portName in availablePorts) {
        try {
          debugPrint('SerialCommunicationService: Trying port: $portName');

          // Check port permissions and existence first
          await _checkPortPermissions(portName);

          _desktopPort = SerialPort(portName);

          // Get port info for debugging
          _logPortInfo(portName);

          // Configure port
          final config = SerialPortConfig();
          if (PlatformUtils.isWindows) {
            config.baudRate = 256000;
          } else {
            config.baudRate = 115200;
          }
          config.bits = 8;
          config.stopBits = 1;
          config.parity = SerialPortParity.none;
          config.setFlowControl(SerialPortFlowControl.none);

          _desktopPort!.config = config;

          // Try to open the port with different modes
          debugPrint('SerialCommunicationService: Attempting to open port: $portName');

          // Try read-write first
          bool opened = _desktopPort!.openReadWrite();
          if (!opened) {
            debugPrint('SerialCommunicationService: Read-write failed, trying read-only');
            opened = _desktopPort!.openRead();
          }

          if (opened) {
            debugPrint('SerialCommunicationService: Successfully opened port: $portName');
            _isConnected = true;

            // Start reading data
            _startDesktopDataReading();
            break;
          } else {
            final lastError = SerialPort.lastError;
            debugPrint('SerialCommunicationService: Failed to open port: $portName, error: $lastError');

            // Try a workaround for permission issues
            await _tryPortWorkaround(portName);

            _desktopPort!.dispose();
            _desktopPort = null;
          }
        } catch (e) {
          debugPrint('SerialCommunicationService: Error with port $portName: $e');
          _desktopPort?.dispose();
          _desktopPort = null;
        }
      }
      
      if (!_isConnected) {
        debugPrint('SerialCommunicationService: Could not connect to any serial port');
      }

    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop initialization with timeout error: $e');
    }
  }

  /// Start reading data from Android USB port
  void _startAndroidDataReading() {
    if (_androidPort == null) return;

    debugPrint('SerialCommunicationService: Starting Android data reading');

    // Listen to the input stream from the USB port
    _androidPort!.inputStream!.listen(
      (data) {
        if (data.isNotEmpty) {
          _processIncomingData(data);
        }
      },
      onError: (error) {
        debugPrint('SerialCommunicationService: Android read error: $error');
      },
    );
  }

  /// Start reading data from desktop serial port
  void _startDesktopDataReading() {
    if (_desktopPort == null) return;
    
    debugPrint('SerialCommunicationService: Starting desktop data reading');
    
    _readTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      try {
        final reader = SerialPortReader(_desktopPort!);
        reader.stream.listen(
          (data) {
            _processIncomingData(Uint8List.fromList(data));
          },
          onError: (error) {
            debugPrint('SerialCommunicationService: Desktop read error: $error');
          },
        );
      } catch (e) {
        debugPrint('SerialCommunicationService: Desktop read setup error: $e');
      }
    });
  }

  /// Process incoming data and extract distance
  void _processIncomingData(dynamic data) {
    List<int> dataList;
    if (data is List<int>) {
      dataList = data;
    } else {
      debugPrint('SerialCommunicationService: Unknown data type: ${data.runtimeType}');
      return;
    }

    // Add new data to buffer
    _dataBuffer.addAll(dataList);

    // Keep buffer size reasonable
    if (_dataBuffer.length > 1000) {
      _dataBuffer = _dataBuffer.sublist(_dataBuffer.length - 500);
    }

    // Parse distance from buffer
    int? distance = parseDistance(_dataBuffer);
    if (distance != null) {
      _distanceController?.add(distance);
    }
  }

  /// Parse distance from LD2410B data
  /// Returns distance in centimeters or null if no valid data found
  int? parseDistance(List<int> data) {
    for (int i = 0; i < data.length - 9; i++) {
      if (data[i] == 0xFD && data[i + 1] == 0xFC) {
        int distanceLow = data[i + 8];
        int distanceHigh = data[i + 9];
        return (distanceHigh << 8) + distanceLow;
      }
    }
    return null;
  }

  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    debugPrint('SerialCommunicationService: Disposing');
    
    _readTimer?.cancel();
    _readTimer = null;
    
    if (PlatformUtils.isAndroid && _androidPort != null) {
      await _androidPort!.close();
      _androidPort = null;
    }
    
    if (PlatformUtils.isDesktop && _desktopPort != null) {
      _desktopPort!.close();
      _desktopPort!.dispose();
      _desktopPort = null;
    }
    
    await _distanceController?.close();
    _distanceController = null;
    _distanceStream = null;
    
    _isConnected = false;
    _isInitialized = false;
    _dataBuffer.clear();
  }

  /// Check port permissions and existence
  Future<void> _checkPortPermissions(String portName) async {
    try {
      debugPrint('SerialCommunicationService: Checking permissions for port: $portName');

      // Check if file exists
      final file = File(portName);
      final exists = await file.exists();
      debugPrint('SerialCommunicationService: Port $portName exists: $exists');

      if (exists) {
        // Check file permissions
        final stat = await file.stat();
        debugPrint('SerialCommunicationService: Port $portName permissions: ${stat.mode.toRadixString(8)}');

        // Check if readable and writable
        final readable = await file.readAsBytes().then((_) => true).catchError((_) => false);
        debugPrint('SerialCommunicationService: Port $portName readable: $readable');
      }

      // Check current user groups
      final result = await Process.run('groups', []);
      debugPrint('SerialCommunicationService: Current user groups: ${result.stdout}');

      // Check port ownership
      final lsResult = await Process.run('ls', ['-la', portName]);
      debugPrint('SerialCommunicationService: Port ownership: ${lsResult.stdout}');

    } catch (e) {
      debugPrint('SerialCommunicationService: Error checking port permissions: $e');
    }
  }

  /// Log port information for debugging
  void _logPortInfo(String portName) {
    try {
      final port = SerialPort(portName);
      debugPrint('SerialCommunicationService: Port info for $portName:');
      debugPrint('  - Description: ${port.description ?? 'N/A'}');
      debugPrint('  - Manufacturer: ${port.manufacturer ?? 'N/A'}');
      debugPrint('  - Product ID: ${port.productId ?? 'N/A'}');
      debugPrint('  - Vendor ID: ${port.vendorId ?? 'N/A'}');
      debugPrint('  - Serial Number: ${port.serialNumber ?? 'N/A'}');
      port.dispose();
    } catch (e) {
      debugPrint('SerialCommunicationService: Error getting port info for $portName: $e');
    }
  }

  /// Try workaround for port access issues
  Future<void> _tryPortWorkaround(String portName) async {
    try {
      debugPrint('SerialCommunicationService: Trying workaround for port: $portName');

      // Check if we can temporarily change permissions
      final chmodResult = await Process.run('sudo', ['chmod', '666', portName]);
      if (chmodResult.exitCode == 0) {
        debugPrint('SerialCommunicationService: Temporarily changed permissions for $portName');

        // Try to open again
        final tempPort = SerialPort(portName);
        if (tempPort.openReadWrite()) {
          debugPrint('SerialCommunicationService: Workaround successful for $portName');
          tempPort.close();
          tempPort.dispose();
        }
      } else {
        debugPrint('SerialCommunicationService: Permission workaround failed: ${chmodResult.stderr}');
      }
    } catch (e) {
      debugPrint('SerialCommunicationService: Workaround error: $e');
    }
  }
}
