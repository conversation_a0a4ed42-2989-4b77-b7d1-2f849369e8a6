import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:signage/utils/platform_utils.dart';

// Platform-specific imports
import 'package:flutter_libserialport/flutter_libserialport.dart' if (dart.library.html) 'dart:html';
import 'package:usb_serial/usb_serial.dart' if (dart.library.html) 'dart:html';

/// Cross-platform serial communication service for LD2410B sensor
class SerialCommunicationService {
  static final SerialCommunicationService _instance = SerialCommunicationService._internal();
  factory SerialCommunicationService() => _instance;
  SerialCommunicationService._internal();

  // Platform-specific controllers
  SerialPort? _desktopPort;
  UsbPort? _androidPort;
  
  // Connection state
  bool _isConnected = false;
  bool _isInitialized = false;
  
  // Data stream
  StreamController<int?>? _distanceController;
  Stream<int?>? _distanceStream;
  
  // Buffer for incoming data
  List<int> _dataBuffer = [];
  
  // Timer for periodic data reading
  Timer? _readTimer;

  /// Get the distance stream
  Stream<int?>? get distanceStream => _distanceStream;

  /// Check if the service is connected
  bool get isConnected => _isConnected;

  /// Initialize the serial communication service
  Future<bool> initialize() async {
    if (_isInitialized) {
      debugPrint('SerialCommunicationService: Already initialized');
      return _isConnected;
    }

    debugPrint('SerialCommunicationService: Initializing for platform: ${PlatformUtils.platformName}');
    
    try {
      _distanceController = StreamController<int?>.broadcast();
      _distanceStream = _distanceController!.stream;
      
      if (PlatformUtils.isAndroid) {
        await _initializeAndroid();
      } else if (PlatformUtils.isDesktop) {
        await _initializeDesktop();
      } else {
        debugPrint('SerialCommunicationService: Unsupported platform');
        return false;
      }
      
      _isInitialized = true;
      debugPrint('SerialCommunicationService: Initialization completed. Connected: $_isConnected');
      return _isConnected;
    } catch (e) {
      debugPrint('SerialCommunicationService: Initialization failed: $e');
      return false;
    }
  }

  /// Initialize Android USB serial communication
  Future<void> _initializeAndroid() async {
    try {
      debugPrint('SerialCommunicationService: Initializing Android USB serial');

      // Note: This is a placeholder implementation
      // The actual implementation would use the usb_serial package
      // but the exact API may vary. This provides the structure.

      debugPrint('SerialCommunicationService: Android USB serial initialization placeholder');
      debugPrint('SerialCommunicationService: In production, this would:');
      debugPrint('SerialCommunicationService: 1. List USB devices');
      debugPrint('SerialCommunicationService: 2. Find LD2410B device by VID/PID');
      debugPrint('SerialCommunicationService: 3. Create and configure USB port');
      debugPrint('SerialCommunicationService: 4. Start data reading');

      // For now, mark as not connected to prevent errors
      _isConnected = false;

    } catch (e) {
      debugPrint('SerialCommunicationService: Android initialization error: $e');
    }
  }

  /// Initialize desktop serial communication
  Future<void> _initializeDesktop() async {
    try {
      debugPrint('SerialCommunicationService: Initializing desktop serial');
      
      // Get list of available serial ports
      final availablePorts = SerialPort.availablePorts;
      debugPrint('SerialCommunicationService: Available ports: $availablePorts');
      
      if (availablePorts.isEmpty) {
        debugPrint('SerialCommunicationService: No serial ports available');
        return;
      }
      
      // Try to find LD2410B device by testing each port
      for (String portName in availablePorts) {
        try {
          debugPrint('SerialCommunicationService: Trying port: $portName');
          
          _desktopPort = SerialPort(portName);
          
          // Configure port
          final config = SerialPortConfig();
          if (PlatformUtils.isWindows) {
            config.baudRate = 256000;
          } else {
            config.baudRate = 115200;
          }
          config.bits = 8;
          config.stopBits = 1;
          config.parity = SerialPortParity.none;
          config.setFlowControl(SerialPortFlowControl.none);
          
          _desktopPort!.config = config;
          
          // Try to open the port
          if (_desktopPort!.openReadWrite()) {
            debugPrint('SerialCommunicationService: Successfully opened port: $portName');
            _isConnected = true;
            
            // Start reading data
            _startDesktopDataReading();
            break;
          } else {
            debugPrint('SerialCommunicationService: Failed to open port: $portName');
            _desktopPort!.dispose();
            _desktopPort = null;
          }
        } catch (e) {
          debugPrint('SerialCommunicationService: Error with port $portName: $e');
          _desktopPort?.dispose();
          _desktopPort = null;
        }
      }
      
      if (!_isConnected) {
        debugPrint('SerialCommunicationService: Could not connect to any serial port');
      }
      
    } catch (e) {
      debugPrint('SerialCommunicationService: Desktop initialization error: $e');
    }
  }



  /// Start reading data from desktop serial port
  void _startDesktopDataReading() {
    if (_desktopPort == null) return;
    
    debugPrint('SerialCommunicationService: Starting desktop data reading');
    
    _readTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      try {
        final reader = SerialPortReader(_desktopPort!);
        reader.stream.listen(
          (data) {
            _processIncomingData(Uint8List.fromList(data));
          },
          onError: (error) {
            debugPrint('SerialCommunicationService: Desktop read error: $error');
          },
        );
      } catch (e) {
        debugPrint('SerialCommunicationService: Desktop read setup error: $e');
      }
    });
  }

  /// Process incoming data and extract distance
  void _processIncomingData(dynamic data) {
    List<int> dataList;
    if (data is List<int>) {
      dataList = data;
    } else {
      debugPrint('SerialCommunicationService: Unknown data type: ${data.runtimeType}');
      return;
    }

    // Add new data to buffer
    _dataBuffer.addAll(dataList);

    // Keep buffer size reasonable
    if (_dataBuffer.length > 1000) {
      _dataBuffer = _dataBuffer.sublist(_dataBuffer.length - 500);
    }

    // Parse distance from buffer
    int? distance = parseDistance(_dataBuffer);
    if (distance != null) {
      _distanceController?.add(distance);
    }
  }

  /// Parse distance from LD2410B data
  /// Returns distance in centimeters or null if no valid data found
  int? parseDistance(List<int> data) {
    for (int i = 0; i < data.length - 9; i++) {
      if (data[i] == 0xFD && data[i + 1] == 0xFC) {
        int distanceLow = data[i + 8];
        int distanceHigh = data[i + 9];
        return (distanceHigh << 8) + distanceLow;
      }
    }
    return null;
  }

  /// Dispose the service and clean up resources
  Future<void> dispose() async {
    debugPrint('SerialCommunicationService: Disposing');
    
    _readTimer?.cancel();
    _readTimer = null;
    
    if (PlatformUtils.isAndroid && _androidPort != null) {
      await _androidPort!.close();
      _androidPort = null;
    }
    
    if (PlatformUtils.isDesktop && _desktopPort != null) {
      _desktopPort!.close();
      _desktopPort!.dispose();
      _desktopPort = null;
    }
    
    await _distanceController?.close();
    _distanceController = null;
    _distanceStream = null;
    
    _isConnected = false;
    _isInitialized = false;
    _dataBuffer.clear();
  }
}
